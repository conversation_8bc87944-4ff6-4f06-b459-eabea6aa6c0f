package io.gigsta.presentation.home

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import io.gigsta.presentation.home.components.HomeBottomNavigationBar
import io.gigsta.presentation.home.components.HomeContent
import io.gigsta.presentation.home.components.HomeTopBar

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    viewModel: HomeViewModel = viewModel(),
    onSignOut: () -> Unit = {},
    onNavigateToEmailApplication: () -> Unit = {},
    onNavigateToApplicationLetter: () -> Unit = {}
) {
    val uiState = viewModel.uiState
    val currentMenuItem = viewModel.menuItems[uiState.selectedTabIndex]

    // Set navigation callbacks in the ViewModel
    LaunchedEffect(Unit) {
        viewModel.setNavigationCallbacks(
            onNavigateToEmailApplication = onNavigateToEmailApplication,
            onNavigateToApplicationLetter = onNavigateToApplicationLetter
        )
    }

    Scaffold(
        topBar = {
            HomeTopBar(
                title = currentMenuItem.title,
                onMenuClick = { /* Handle menu */ },
                onSearchClick = { /* Handle search */ },
                onSignOut = onSignOut
            )
        },
        bottomBar = {
            HomeBottomNavigationBar(
                selectedTabIndex = uiState.selectedTabIndex,
                onTabSelected = viewModel::onTabSelected,
                menuItems = viewModel.menuItems
            )
        }
    ) { paddingValues ->
        HomeContent(
            modifier = Modifier.padding(paddingValues),
            menuItem = currentMenuItem,
            historyItems = uiState.historyItems,
            isLoading = uiState.isLoading,
            error = uiState.error,
            onCreateNewItem = viewModel::onCreateNewItem,
            onHistoryItemClick = viewModel::onHistoryItemClick
        )
    }
}

