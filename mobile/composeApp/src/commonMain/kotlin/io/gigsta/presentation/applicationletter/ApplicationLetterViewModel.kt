package io.gigsta.presentation.applicationletter

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import co.touchlab.kermit.Logger
import io.gigsta.domain.model.*
import io.gigsta.domain.utils.StructuredLetterData
import io.gigsta.domain.utils.LetterTemplate
import io.gigsta.domain.utils.getAllLetterTemplates
import io.gigsta.domain.usecase.UploadResumeUseCase
import io.gigsta.domain.usecase.GetExistingResumeUseCase
import io.gigsta.domain.usecase.DeleteResumeUseCase
import io.gigsta.domain.usecase.GetResumeUrlUseCase
import io.gigsta.domain.usecase.GenerateApplicationLetterUseCase
import io.gigsta.domain.usecase.ListenForLetterStatusUseCase
import io.gigsta.domain.usecase.EstablishRealtimeConnectionUseCase
import io.gigsta.data.utils.LetterGenerationStatus
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onStart

class ApplicationLetterViewModel(
    private val generateApplicationLetterUseCase: GenerateApplicationLetterUseCase,
    private val uploadResumeUseCase: UploadResumeUseCase,
    private val getExistingResumeUseCase: GetExistingResumeUseCase,
    private val deleteResumeUseCase: DeleteResumeUseCase,
    private val getResumeUrlUseCase: GetResumeUrlUseCase,
    private val listenForLetterStatusUseCase: ListenForLetterStatusUseCase,
    private val establishRealtimeConnectionUseCase: EstablishRealtimeConnectionUseCase
) : ViewModel() {

    var uiState by mutableStateOf(ApplicationLetterUiState())
        private set

    private var generateLetterJob: Job? = null
    private var letterStatusJob: Job? = null

    init {
        loadExistingResume()
        loadAvailableTemplates()
    }

    fun onStepChanged(step: LetterGenerationStep) {
        uiState = uiState.copy(currentStep = step)
    }

    fun onInputMethodChanged(inputMethod: InputMethod) {
        uiState = uiState.copy(
            inputMethod = inputMethod,
            error = null
        )
    }

    fun onJobDescriptionChanged(description: String) {
        uiState = uiState.copy(
            jobDescription = description,
            error = null
        )
    }

    fun onTemplateSelected(template: LetterTemplate) {
        uiState = uiState.copy(
            selectedTemplate = template,
            error = null
        )
    }

    fun onResumeUploaded(fileData: ByteArray, fileName: String, mimeType: String) {
        uiState = uiState.copy(isResumeUploading = true, error = null)

        viewModelScope.launch {
            try {
                val result = uploadResumeUseCase(fileData, fileName, mimeType)
                handleResumeUploadResult(result)
            } catch (e: Exception) {
                handleResumeUploadError(e)
            }
        }
    }

    private fun handleResumeUploadResult(result: Result<ResumeInfo>) {
        result.fold(
            onSuccess = { resumeInfo ->
                uiState = uiState.copy(
                    resumeInfo = resumeInfo,
                    isResumeUploaded = true,
                    isResumeUploading = false,
                    error = null
                )
            },
            onFailure = { exception ->
                Logger.e(exception) { "Failed to upload resume: ${exception.message}" }
                uiState = uiState.copy(
                    isResumeUploading = false,
                    error = exception.message ?: "Gagal mengunggah resume"
                )
            }
        )
    }

    private fun handleResumeUploadError(exception: Exception) {
        Logger.e(exception) { "Failed to upload resume: ${exception.message}" }
        uiState = uiState.copy(
            isResumeUploading = false,
            error = exception.message ?: "Terjadi kesalahan saat mengunggah resume"
        )
    }

    fun onJobImageSelected(imageData: ByteArray, fileName: String, mimeType: String) {
        uiState = uiState.copy(
            jobImageData = imageData,
            jobImageMimeType = mimeType,
            jobImageFileName = fileName,
            hasJobImage = true,
            error = null
        )
    }

    fun onViewResume() {
        val resumeInfo = uiState.resumeInfo ?: return

        uiState = uiState.copy(isGettingResumeUrl = true)

        viewModelScope.launch {
            try {
                val result = getResumeUrlUseCase(resumeInfo)
                handleResumeUrlResult(result)
            } catch (e: Exception) {
                handleResumeUrlError(e)
            }
        }
    }

    private fun handleResumeUrlResult(result: Result<String>) {
        result.fold(
            onSuccess = { url ->
                uiState = uiState.copy(
                    isGettingResumeUrl = false,
                    resumeUrl = url,
                    shouldOpenResumeUrl = true
                )
            },
            onFailure = { exception ->
                uiState = uiState.copy(
                    isGettingResumeUrl = false,
                    error = exception.message ?: "Gagal membuka resume"
                )
            }
        )
    }

    private fun handleResumeUrlError(exception: Exception) {
        uiState = uiState.copy(
            isGettingResumeUrl = false,
            error = exception.message ?: "Terjadi kesalahan saat membuka resume"
        )
    }

    fun onDeleteResume() {
        uiState = uiState.copy(isResumeDeleting = true, error = null)

        viewModelScope.launch {
            try {
                val result = deleteResumeUseCase()
                handleResumeDeleteResult(result)
            } catch (e: Exception) {
                handleResumeDeleteError(e)
            }
        }
    }

    private fun handleResumeDeleteResult(result: Result<Unit>) {
        result.fold(
            onSuccess = {
                uiState = uiState.copy(
                    resumeInfo = null,
                    isResumeUploaded = false,
                    isResumeDeleting = false,
                    error = null
                )
            },
            onFailure = { exception ->
                uiState = uiState.copy(
                    isResumeDeleting = false,
                    error = exception.message ?: "Gagal menghapus resume"
                )
            }
        )
    }

    private fun handleResumeDeleteError(exception: Exception) {
        uiState = uiState.copy(
            isResumeDeleting = false,
            error = exception.message ?: "Terjadi kesalahan saat menghapus resume"
        )
    }

    fun generateLetter() {
        if (!validateInputs()) return

        val resumeInfo = uiState.resumeInfo!!
        val jobInfo = createJobInfo() ?: return
        val templateId = uiState.selectedTemplate!!.id

        uiState = uiState.copy(isGenerating = true, error = null)

        generateLetterJob = viewModelScope.launch {
            try {
                val result = generateApplicationLetterUseCase(resumeInfo, jobInfo, templateId)
                handleGenerationResult(result)
            } catch (e: Exception) {
                handleGenerationError(e)
            }
        }
    }

    private fun validateInputs(): Boolean {
        when {
            uiState.resumeInfo == null -> {
                uiState = uiState.copy(error = "Harap unggah resume terlebih dahulu")
                return false
            }
            uiState.selectedTemplate == null -> {
                uiState = uiState.copy(error = "Harap pilih template surat lamaran")
                return false
            }
            else -> return true
        }
    }

    private fun createJobInfo(): JobInfo? {
        return when (uiState.inputMethod) {
            InputMethod.TEXT -> {
                if (uiState.jobDescription.isBlank()) {
                    uiState = uiState.copy(error = "Harap isi deskripsi lowongan")
                    null
                } else {
                    JobInfo(description = uiState.jobDescription)
                }
            }
            InputMethod.IMAGE -> {
                if (uiState.jobImageData == null) {
                    uiState = uiState.copy(error = "Harap unggah gambar lowongan")
                    null
                } else {
                    JobInfo(
                        imageData = uiState.jobImageData,
                        imageMimeType = uiState.jobImageMimeType,
                        imageFileName = uiState.jobImageFileName
                    )
                }
            }
        }
    }

    private suspend fun handleGenerationResult(result: Result<ApplicationLetter>) {
        result.fold(
            onSuccess = { applicationLetter ->
                uiState = uiState.copy(applicationLetter = applicationLetter, error = null)
                startListeningForLetterStatus(applicationLetter.id)
            },
            onFailure = { exception ->
                Logger.e(exception) { "Failed to start letter generation: ${exception.message}" }
                uiState = uiState.copy(
                    isGenerating = false,
                    error = exception.message ?: "Terjadi kesalahan saat memulai pembuatan surat lamaran"
                )
            }
        )
    }

    private fun handleGenerationError(exception: Exception) {
        Logger.e(exception) { "Failed to start letter generation: ${exception.message}" }
        uiState = uiState.copy(
            isGenerating = false,
            error = exception.message ?: "Terjadi kesalahan saat memulai pembuatan surat lamaran"
        )
    }

    fun regenerateLetter() {
        generateLetter()
    }

    fun cancelGeneration() {
        generateLetterJob?.cancel()
        generateLetterJob = null
        letterStatusJob?.cancel()
        letterStatusJob = null
        uiState = uiState.copy(
            isGenerating = false,
            error = null,
            applicationLetter = null
        )
    }

    private fun startListeningForLetterStatus(letterId: String) {
        letterStatusJob?.cancel()

        letterStatusJob = viewModelScope.launch {
            try {
                establishRealtimeConnection()
                loadInitialLetterStatus(letterId)
                observeLetterStatusChanges(letterId)
            } catch (e: Exception) {
                handleStatusListenerError(e)
            }
        }
    }

    private suspend fun establishRealtimeConnection() {
        establishRealtimeConnectionUseCase().fold(
            onSuccess = {
                Logger.d { "Realtime connection established" }
            },
            onFailure = { exception ->
                Logger.e(exception) { "Failed to establish realtime connection: ${exception.message}" }
            }
        )
    }

    private suspend fun loadInitialLetterStatus(letterId: String) {
        listenForLetterStatusUseCase.getInitialStatus(letterId).fold(
            onSuccess = { initialStatus ->
                updateUIFromLetterStatus(initialStatus, letterId)
            },
            onFailure = { exception ->
                Logger.e(exception) { "Failed to get initial letter status: ${exception.message}" }
            }
        )
    }

    private suspend fun observeLetterStatusChanges(letterId: String) {
        listenForLetterStatusUseCase(letterId)
            .onStart {
                Logger.d { "Started listening for letter status changes for ID: $letterId" }
            }
            .catch { exception ->
                Logger.e(exception) { "Error in letter status listener: ${exception.message}" }
                // Only show error if we're still generating (not if generation already completed successfully)
                if (uiState.isGenerating && uiState.applicationLetter == null) {
                    uiState = uiState.copy(
                        isGenerating = false,
                        error = "Terjadi kesalahan saat memantau status pembuatan surat"
                    )
                } else {
                    Logger.d { "Ignoring status listener error since letter generation already completed" }
                }
            }
            .collect { status ->
                Logger.d { "Received letter status update: $status" }
                updateUIFromLetterStatus(status, letterId)
            }
    }

    private fun handleStatusListenerError(exception: Exception) {
        Logger.e(exception) { "Error setting up letter status listener: ${exception.message}" }
        // Only show error if we're still generating and haven't completed successfully
        if (uiState.isGenerating && uiState.applicationLetter == null) {
            uiState = uiState.copy(
                isGenerating = false,
                error = "Terjadi kesalahan saat memantau status pembuatan surat"
            )
        } else {
            Logger.d { "Ignoring status listener setup error since letter generation already completed" }
        }
    }

    private fun updateUIFromLetterStatus(status: LetterGenerationStatus, letterId: String) {
        when (status) {
            is LetterGenerationStatus.Processing -> handleProcessingStatus()
            is LetterGenerationStatus.Done -> handleDoneStatus(status, letterId)
            is LetterGenerationStatus.Error -> handleErrorStatus(status)
            is LetterGenerationStatus.Idle -> handleIdleStatus()
        }
    }

    private fun handleProcessingStatus() {
        uiState = uiState.copy(isGenerating = true, error = null)
    }

    private fun handleDoneStatus(status: LetterGenerationStatus.Done, letterId: String) {
        val structuredData = parseStructuredData(status.structuredData)
        val updatedLetter = createApplicationLetter(
            letterId = letterId,
            plainText = status.plainText,
            structuredData = structuredData,
            designHtml = status.designHtml
        )

        uiState = uiState.copy(
            isGenerating = false,
            applicationLetter = updatedLetter,
            error = null
        )

        stopListening()
    }

    private fun handleErrorStatus(status: LetterGenerationStatus.Error) {
        // Don't override a successful completion with an error
        if (uiState.applicationLetter == null) {
            uiState = uiState.copy(isGenerating = false, error = status.error)
            stopListening()
        } else {
            Logger.d { "Ignoring error status since letter generation already completed successfully" }
        }
    }

    private fun handleIdleStatus() {
        uiState = uiState.copy(isGenerating = false)
    }

    private fun parseStructuredData(structuredDataJson: String?): StructuredLetterData {
        return if (!structuredDataJson.isNullOrBlank()) {
            try {
                kotlinx.serialization.json.Json.decodeFromString(
                    StructuredLetterData.serializer(),
                    structuredDataJson
                )
            } catch (e: Exception) {
                Logger.e(e) { "Failed to parse structured data: ${e.message}" }
                createDefaultStructuredData()
            }
        } else {
            createDefaultStructuredData()
        }
    }

    private fun createDefaultStructuredData(): StructuredLetterData {
        return io.gigsta.domain.utils.createDefaultStructuredLetterData(
            templateId = uiState.selectedTemplate?.id ?: "plain-text",
            language = "id"
        )
    }

    private fun createApplicationLetter(
        letterId: String,
        plainText: String,
        structuredData: StructuredLetterData,
        designHtml: String? = null
    ): ApplicationLetter {
        return ApplicationLetter(
            id = letterId,
            plainText = plainText,
            structuredData = structuredData,
            templateId = uiState.selectedTemplate?.id ?: "plain-text",
            templateName = uiState.selectedTemplate?.name ?: "Generated Letter",
            designHtml = designHtml
        )
    }

    private fun stopListening() {
        letterStatusJob?.cancel()
        letterStatusJob = null
    }

    fun goToNextStep() {
        when (uiState.currentStep) {
            LetterGenerationStep.RESUME_UPLOAD -> {
                if (uiState.isResumeUploaded) {
                    uiState = uiState.copy(currentStep = LetterGenerationStep.TEMPLATE_SELECTION)
                } else {
                    uiState = uiState.copy(error = "Harap unggah resume terlebih dahulu")
                }
            }
            LetterGenerationStep.TEMPLATE_SELECTION -> {
                if (uiState.selectedTemplate != null) {
                    uiState = uiState.copy(currentStep = LetterGenerationStep.JOB_INFO)
                } else {
                    uiState = uiState.copy(error = "Harap pilih template surat lamaran")
                }
            }
            LetterGenerationStep.JOB_INFO -> {
                generateLetter()
            }
            LetterGenerationStep.RESULT -> {
                // Already at the last step
            }
        }
    }

    fun goToPreviousStep() {
        when (uiState.currentStep) {
            LetterGenerationStep.RESUME_UPLOAD -> {
                // Already at the first step
            }
            LetterGenerationStep.TEMPLATE_SELECTION -> {
                uiState = uiState.copy(currentStep = LetterGenerationStep.RESUME_UPLOAD)
            }
            LetterGenerationStep.JOB_INFO -> {
                uiState = uiState.copy(currentStep = LetterGenerationStep.TEMPLATE_SELECTION)
            }
            LetterGenerationStep.RESULT -> {
                uiState = uiState.copy(currentStep = LetterGenerationStep.JOB_INFO)
            }
        }
    }

    fun clearError() {
        uiState = uiState.copy(error = null)
    }

    fun onResumeUrlOpened() {
        uiState = uiState.copy(shouldOpenResumeUrl = false)
    }

    private fun loadExistingResume() {
        uiState = uiState.copy(isResumeLoading = true)

        viewModelScope.launch {
            try {
                val result = getExistingResumeUseCase()
                handleExistingResumeResult(result)
            } catch (e: Exception) {
                handleExistingResumeError(e)
            }
        }
    }

    private fun handleExistingResumeResult(result: Result<ResumeInfo?>) {
        result.fold(
            onSuccess = { resumeInfo ->
                uiState = uiState.copy(
                    resumeInfo = resumeInfo,
                    isResumeUploaded = resumeInfo != null,
                    isResumeLoading = false
                )
            },
            onFailure = { exception ->
                uiState = uiState.copy(
                    isResumeLoading = false,
                    error = exception.message ?: "Gagal memuat resume yang ada"
                )
            }
        )
    }

    private fun handleExistingResumeError(exception: Exception) {
        uiState = uiState.copy(
            isResumeLoading = false,
            error = exception.message ?: "Terjadi kesalahan saat memuat resume"
        )
    }

    private fun loadAvailableTemplates() {
        uiState = uiState.copy(availableTemplates = getAllLetterTemplates())
    }

}

data class ApplicationLetterUiState(
    val currentStep: LetterGenerationStep = LetterGenerationStep.RESUME_UPLOAD,
    val inputMethod: InputMethod = InputMethod.TEXT,
    val jobDescription: String = "",
    val jobImageData: ByteArray? = null,
    val jobImageMimeType: String? = null,
    val jobImageFileName: String? = null,
    val hasJobImage: Boolean = false,
    val resumeInfo: ResumeInfo? = null,
    val isResumeUploaded: Boolean = false,
    val isResumeLoading: Boolean = false,
    val isResumeUploading: Boolean = false,
    val isResumeDeleting: Boolean = false,
    val isGettingResumeUrl: Boolean = false,
    val resumeUrl: String? = null,
    val shouldOpenResumeUrl: Boolean = false,
    val availableTemplates: List<LetterTemplate> = emptyList(),
    val selectedTemplate: LetterTemplate? = null,
    val isGenerating: Boolean = false,
    val applicationLetter: ApplicationLetter? = null,
    val error: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as ApplicationLetterUiState

        if (currentStep != other.currentStep) return false
        if (inputMethod != other.inputMethod) return false
        if (jobDescription != other.jobDescription) return false
        if (jobImageData != null) {
            if (other.jobImageData == null) return false
            if (!jobImageData.contentEquals(other.jobImageData)) return false
        } else if (other.jobImageData != null) return false
        if (jobImageMimeType != other.jobImageMimeType) return false
        if (hasJobImage != other.hasJobImage) return false
        if (resumeInfo != other.resumeInfo) return false
        if (isResumeUploaded != other.isResumeUploaded) return false
        if (isResumeLoading != other.isResumeLoading) return false
        if (isResumeUploading != other.isResumeUploading) return false
        if (isResumeDeleting != other.isResumeDeleting) return false
        if (isGettingResumeUrl != other.isGettingResumeUrl) return false
        if (resumeUrl != other.resumeUrl) return false
        if (shouldOpenResumeUrl != other.shouldOpenResumeUrl) return false
        if (availableTemplates != other.availableTemplates) return false
        if (selectedTemplate != other.selectedTemplate) return false
        if (isGenerating != other.isGenerating) return false
        if (applicationLetter != other.applicationLetter) return false
        if (error != other.error) return false

        return true
    }

    override fun hashCode(): Int {
        var result = currentStep.hashCode()
        result = 31 * result + inputMethod.hashCode()
        result = 31 * result + jobDescription.hashCode()
        result = 31 * result + (jobImageData?.contentHashCode() ?: 0)
        result = 31 * result + (jobImageMimeType?.hashCode() ?: 0)
        result = 31 * result + hasJobImage.hashCode()
        result = 31 * result + (resumeInfo?.hashCode() ?: 0)
        result = 31 * result + isResumeUploaded.hashCode()
        result = 31 * result + isResumeLoading.hashCode()
        result = 31 * result + isResumeUploading.hashCode()
        result = 31 * result + isResumeDeleting.hashCode()
        result = 31 * result + isGettingResumeUrl.hashCode()
        result = 31 * result + (resumeUrl?.hashCode() ?: 0)
        result = 31 * result + shouldOpenResumeUrl.hashCode()
        result = 31 * result + availableTemplates.hashCode()
        result = 31 * result + (selectedTemplate?.hashCode() ?: 0)
        result = 31 * result + isGenerating.hashCode()
        result = 31 * result + (applicationLetter?.hashCode() ?: 0)
        result = 31 * result + (error?.hashCode() ?: 0)
        return result
    }
}

enum class LetterGenerationStep {
    RESUME_UPLOAD,
    TEMPLATE_SELECTION,
    JOB_INFO,
    RESULT
}

data class ApplicationLetter(
    val id: String,
    val plainText: String,
    val structuredData: StructuredLetterData,
    val templateId: String,
    val templateName: String,
    val designHtml: String? = null
)