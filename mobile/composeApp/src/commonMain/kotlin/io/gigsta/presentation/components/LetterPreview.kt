package io.gigsta.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Remove
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.multiplatform.webview.web.WebView
import com.multiplatform.webview.web.rememberWebViewState

@Composable
fun LetterPreview(
    htmlContent: String,
    templateName: String,
    modifier: Modifier = Modifier
) {
    var zoomLevel by remember { mutableStateOf(100) }
    var webViewError by remember { mutableStateOf<String?>(null) }
    val webViewState = rememberWebViewState(url = "")

    // Create HTML content with minimal styling for zoom support only
    val styledHtmlContent = remember(htmlContent, zoomLevel) {
        """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    margin: 0;
                    padding: 16px;
                    background-color: #f5f5f5;
                    zoom: ${zoomLevel}%;
                    -webkit-transform: scale(${zoomLevel / 100.0});
                    -webkit-transform-origin: 0 0;
                    transform: scale(${zoomLevel / 100.0});
                    transform-origin: 0 0;
                }
                .a4-page {
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                }
            </style>
        </head>
        <body>
            $htmlContent
        </body>
        </html>
        """.trimIndent()
    }
    
    Column(modifier = modifier) {
        // Header with template name and zoom controls
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = templateName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = "Pratinjau Surat Lamaran",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // Zoom controls
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(
                        onClick = { 
                            if (zoomLevel > 50) zoomLevel -= 10 
                        },
                        enabled = zoomLevel > 50
                    ) {
                        Icon(
                            imageVector = Icons.Default.Remove,
                            contentDescription = "Perkecil",
                            tint = if (zoomLevel > 50) MaterialTheme.colorScheme.primary 
                                  else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        )
                    }
                    
                    Text(
                        text = "${zoomLevel}%",
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier
                            .background(
                                MaterialTheme.colorScheme.surface,
                                RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                    
                    IconButton(
                        onClick = { 
                            if (zoomLevel < 200) zoomLevel += 10 
                        },
                        enabled = zoomLevel < 200
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Perbesar",
                            tint = if (zoomLevel < 200) MaterialTheme.colorScheme.primary 
                                  else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                        )
                    }
                    
                    IconButton(
                        onClick = { zoomLevel = 100 }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Refresh,
                            contentDescription = "Reset Zoom",
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // WebView container with error handling
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFF5F5F5)
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(8.dp))
            ) {
                if (webViewError != null) {
                    // Fallback content when WebView fails
                    LetterPreviewFallback(
                        htmlContent = htmlContent,
                        error = webViewError!!,
                        modifier = Modifier.fillMaxSize()
                    )
                } else {
                    try {
                        WebView(
                            state = webViewState,
                            modifier = Modifier.fillMaxSize(),
                            onCreated = {
                                // WebView created successfully
                            }
                        )

                        // Load HTML content
                        LaunchedEffect(styledHtmlContent) {
                            try {
                                webViewState.loadHtml(styledHtmlContent)
                            } catch (e: Exception) {
                                webViewError = "Failed to load content: ${e.message}"
                            }
                        }
                    } catch (e: Exception) {
                        // If WebView creation fails, show fallback
                        LaunchedEffect(e) {
                            webViewError = "WebView not supported: ${e.message}"
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun LetterPreviewFallback(
    htmlContent: String,
    error: String,
    modifier: Modifier = Modifier
) {
    val scrollState = rememberScrollState()

    Column(
        modifier = modifier
            .verticalScroll(scrollState)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Error message
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.errorContainer
            )
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
                Column {
                    Text(
                        text = "Preview tidak tersedia",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = "Menampilkan konten HTML mentah",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }

        // Raw HTML content as fallback
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Text(
                text = htmlContent,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(16.dp),
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}
