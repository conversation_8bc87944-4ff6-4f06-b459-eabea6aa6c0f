package io.gigsta.presentation.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Remove
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.multiplatform.webview.web.WebView
import com.multiplatform.webview.web.WebContent
import com.multiplatform.webview.web.rememberWebViewState

@Composable
fun LetterPreview(
    htmlContent: String,
    templateName: String,
    modifier: Modifier = Modifier
) {
    var zoom by remember { mutableStateOf(1.0f) }
    var webViewError by remember { mutableStateOf<String?>(null) }
    val webViewState = rememberWebViewState(url = "")

    // Zoom bounds matching web version
    val minZoom = 0.1f
    val maxZoom = 3.0f
    val zoomStep = 0.1f

    // Create HTML content with ONLY the minimal styling from web version
    val styledHtmlContent = remember(htmlContent, zoom) {
        """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {
                    margin: 0;
                    padding: 16px;
                    box-sizing: border-box;
                    width: 100%;
                }
                .a4-page {
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                }
            </style>
        </head>
        <body>
            <div style="
                transform: scale($zoom);
                transform-origin: top left;
                width: ${if (zoom <= 1) "100%" else "${Math.max(100, (100 * zoom).toInt())}%"};
                height: ${if (zoom <= 1) "100%" else "${Math.max(100, (100 * zoom).toInt())}%"};
                position: relative;
                min-width: 100%;
                margin-right: 16px;
                display: inline-block;
            ">
                $htmlContent
            </div>
        </body>
        </html>
        """.trimIndent()
    }
    
    Column(modifier = modifier) {
        // Header with template name and zoom controls
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = templateName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = "Pratinjau Surat Lamaran",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // Zoom controls matching web version
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedButton(
                        onClick = {
                            zoom = (zoom - zoomStep).coerceAtLeast(minZoom)
                        },
                        enabled = zoom > minZoom,
                        modifier = Modifier.size(40.dp),
                        contentPadding = PaddingValues(0.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Remove,
                            contentDescription = "Perkecil",
                            modifier = Modifier.size(16.dp)
                        )
                    }

                    Text(
                        text = "${(zoom * 100).toInt()}%",
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier
                            .background(
                                MaterialTheme.colorScheme.surface,
                                RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                            .widthIn(min = 48.dp),
                        textAlign = TextAlign.Center
                    )

                    OutlinedButton(
                        onClick = {
                            zoom = (zoom + zoomStep).coerceAtMost(maxZoom)
                        },
                        enabled = zoom < maxZoom,
                        modifier = Modifier.size(40.dp),
                        contentPadding = PaddingValues(0.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Perbesar",
                            modifier = Modifier.size(16.dp)
                        )
                    }

                    OutlinedButton(
                        onClick = { zoom = 1.0f },
                        modifier = Modifier.height(40.dp),
                        contentPadding = PaddingValues(horizontal = 8.dp)
                    ) {
                        Text(
                            text = "Reset",
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // WebView container with error handling
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(8.dp))
            ) {
                if (webViewError != null) {
                    // Fallback content when WebView fails
                    LetterPreviewFallback(
                        htmlContent = htmlContent,
                        error = webViewError!!,
                        modifier = Modifier.fillMaxSize()
                    )
                } else {
                    WebView(
                        state = webViewState,
                        modifier = Modifier.fillMaxSize()
                    )

                    // Load HTML content
                    LaunchedEffect(styledHtmlContent) {
                        try {
                            webViewState.content = com.multiplatform.webview.web.WebContent.Html(styledHtmlContent)
                        } catch (e: Exception) {
                            webViewError = "Failed to load content: ${e.message}"
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun LetterPreviewFallback(
    htmlContent: String,
    error: String,
    modifier: Modifier = Modifier
) {
    val scrollState = rememberScrollState()

    Column(
        modifier = modifier
            .verticalScroll(scrollState)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Error message
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.errorContainer
            )
        ) {
            Row(
                modifier = Modifier.padding(12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
                Column {
                    Text(
                        text = "Preview tidak tersedia",
                        style = MaterialTheme.typography.titleSmall,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        fontWeight = FontWeight.SemiBold
                    )
                    Text(
                        text = "Menampilkan konten HTML mentah",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }

        // Raw HTML content as fallback
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            )
        ) {
            Text(
                text = htmlContent,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(16.dp),
                color = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}
