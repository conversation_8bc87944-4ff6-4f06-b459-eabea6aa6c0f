package io.gigsta.presentation.applicationletter.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Diamond
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.Description
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import io.gigsta.domain.utils.LetterTemplate
import io.gigsta.presentation.theme.Spacing
import org.jetbrains.compose.resources.painterResource
import gigsta.composeapp.generated.resources.Res
import gigsta.composeapp.generated.resources.*

@Composable
fun LetterTemplateSelectionSection(
    availableTemplates: List<LetterTemplate>,
    selectedTemplate: LetterTemplate?,
    onTemplateSelected: (LetterTemplate) -> Unit,
    error: String?,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.small)
    ) {
        Text(
            text = "2. Pilih Template Surat Lamaran",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )

        Text(
            text = "Pilih desain yang sesuai dengan kebutuhan Anda",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Spacer(modifier = Modifier.height(Spacing.small))

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(Spacing.medium),
            contentPadding = PaddingValues(horizontal = Spacing.small)
        ) {
            items(availableTemplates) { template ->
                TemplateCard(
                    template = template,
                    isSelected = selectedTemplate?.id == template.id,
                    onSelected = { onTemplateSelected(template) },
                    modifier = Modifier.width(220.dp) // Much larger width for clearer preview
                )
            }
        }

        // Error message
        error?.let { errorMessage ->
            Text(
                text = errorMessage,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = Spacing.medium)
            )
        }
    }
}

@Composable
private fun TemplateCard(
    template: LetterTemplate,
    isSelected: Boolean,
    onSelected: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .clickable { onSelected() }
            .then(
                if (isSelected) {
                    Modifier.border(
                        2.dp,
                        MaterialTheme.colorScheme.primary,
                        RoundedCornerShape(12.dp)
                    )
                } else Modifier
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(Spacing.medium),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Template preview
            TemplatePreview(
                template = template,
                isSelected = isSelected,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(280.dp) // Much larger height to show full letter template (A4 aspect ratio)
            )

            Spacer(modifier = Modifier.height(Spacing.small))

            // Template name and badges row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Template name
                Text(
                    text = template.name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    maxLines = 2, // Allow 2 lines for longer names
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )

                // Badges
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (template.recommended) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = "Recommended",
                            tint = Color(0xFFFFA726),
                            modifier = Modifier.size(16.dp)
                        )
                    }

                    if (template.isPremium) {
                        Icon(
                            imageVector = Icons.Default.Diamond,
                            contentDescription = "Premium",
                            tint = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }

            // Template description
            Text(
                text = template.previewDescription,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Start,
                maxLines = 2, // Allow 2 lines for better description visibility
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.fillMaxWidth(),
                lineHeight = 16.sp // Better line spacing
            )

            Spacer(modifier = Modifier.height(Spacing.small))

            // Token cost and selection indicator
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Token cost
                Text(
                    text = "${template.tokenCost} token",
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Medium,
                    color = if (template.isPremium) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )

                // Selection indicator
                if (isSelected) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = "Selected",
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun TemplatePreview(
    template: LetterTemplate,
    isSelected: Boolean,
    modifier: Modifier = Modifier
) {
    var isLoading by remember(template.id) { mutableStateOf(true) }
    var hasError by remember(template.id) { mutableStateOf(false) }

    val painter = when (template.id) {
        "plain-text" -> painterResource(Res.drawable.plain_text)
        "classic-blue" -> painterResource(Res.drawable.classic_blue)
        "professional-classic" -> painterResource(Res.drawable.professional_classic)
        "minimalist-sidebar" -> painterResource(Res.drawable.minimalist_sidebar)
        "minimalist-border-frame" -> painterResource(Res.drawable.minimalist_border)
        "minimalist-accent" -> painterResource(Res.drawable.minimalist_accent)
        "minimalist-circular-accents" -> painterResource(Res.drawable.minimalist_circular_accent)
        else -> {
            hasError = true
            null
        }
    }

    Box(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .background(
                if (isSelected) {
                    MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
                } else {
                    MaterialTheme.colorScheme.outline.copy(alpha = 0.1f)
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        if (hasError || painter == null) {
            // Fallback when SVG can't be loaded
            TemplateFallbackPreview(template)
        } else {
            // Display the actual template preview
            Image(
                painter = painter,
                contentDescription = "Preview ${template.name}",
                modifier = Modifier
                    .fillMaxSize()
                    .padding(4.dp), // Adequate padding for clean preview
                contentScale = ContentScale.Fit, // Fit to show full template without cropping
                // No color filter - keep preview clear and visible when selected
            )

            // Mark loading as complete
            LaunchedEffect(Unit) {
                isLoading = false
            }
        }

        // Loading indicator
        if (isLoading && !hasError && painter != null) {
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp),
                strokeWidth = 2.dp,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

@Composable
private fun TemplateFallbackPreview(template: LetterTemplate) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Description,
            contentDescription = null,
            modifier = Modifier.size(48.dp), // Larger icon for bigger preview area
            tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = template.name,
            style = MaterialTheme.typography.bodyMedium, // Larger text
            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
            textAlign = TextAlign.Center,
            maxLines = 3,
            fontWeight = FontWeight.Medium
        )
    }
}