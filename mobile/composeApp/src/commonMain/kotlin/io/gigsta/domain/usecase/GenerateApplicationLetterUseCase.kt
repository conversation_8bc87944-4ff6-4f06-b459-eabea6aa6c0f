package io.gigsta.domain.usecase

import io.gigsta.domain.model.JobInfo
import io.gigsta.domain.model.ResumeInfo
import io.gigsta.domain.repository.ApplicationLetterRepository
import io.gigsta.presentation.applicationletter.ApplicationLetter

class GenerateApplicationLetterUseCase(
    private val repository: ApplicationLetterRepository
) {
    suspend operator fun invoke(
        resumeInfo: ResumeInfo,
        jobInfo: JobInfo,
        templateId: String
    ): Result<ApplicationLetter> {
        return repository.generateApplicationLetter(resumeInfo, jobInfo, templateId)
    }
}