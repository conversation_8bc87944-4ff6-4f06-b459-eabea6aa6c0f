package io.gigsta.domain.usecase

import io.gigsta.data.network.SupabaseClient
import io.github.jan.supabase.realtime.realtime

class EstablishRealtimeConnectionUseCase {
    suspend operator fun invoke(): Result<Unit> {
        return try {
            SupabaseClient.client.realtime.connect()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
