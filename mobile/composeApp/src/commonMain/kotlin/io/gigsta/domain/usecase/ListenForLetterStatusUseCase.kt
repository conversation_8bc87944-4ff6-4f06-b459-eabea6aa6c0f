package io.gigsta.domain.usecase

import io.gigsta.data.utils.LetterGenerationStatus
import io.gigsta.data.utils.LetterStatusListener
import io.gigsta.domain.repository.AuthRepository
import kotlinx.coroutines.flow.Flow

class ListenForLetterStatusUseCase(
    private val authRepository: AuthRepository
) {
    private val letterStatusListener = LetterStatusListener(authRepository)
    
    suspend operator fun invoke(letterId: String): Flow<LetterGenerationStatus> {
        return letterStatusListener.listenForLetterStatusChanges(letterId)
    }
    
    suspend fun getInitialStatus(letterId: String): Result<LetterGenerationStatus> {
        return letterStatusListener.getInitialLetterStatus(letterId)
    }
}
