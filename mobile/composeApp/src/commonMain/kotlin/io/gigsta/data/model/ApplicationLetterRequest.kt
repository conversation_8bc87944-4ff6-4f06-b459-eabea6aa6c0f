package io.gigsta.data.model

import kotlinx.serialization.Serializable
import io.gigsta.domain.utils.StructuredLetterData

@Serializable
data class ApplicationLetterRequest(
    val jobDescription: String? = null,
    val jobImage: ByteArray? = null,
    val jobImageMimeType: String? = null,
    val templateId: String,
    val unauthenticatedResumeFile: ByteArray? = null,
    val unauthenticatedResumeFileName: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as ApplicationLetterRequest

        if (jobDescription != other.jobDescription) return false
        if (jobImage != null) {
            if (other.jobImage == null) return false
            if (!jobImage.contentEquals(other.jobImage)) return false
        } else if (other.jobImage != null) return false
        if (jobImageMimeType != other.jobImageMimeType) return false
        if (templateId != other.templateId) return false
        if (unauthenticatedResumeFile != null) {
            if (other.unauthenticatedResumeFile == null) return false
            if (!unauthenticatedResumeFile.contentEquals(other.unauthenticatedResumeFile)) return false
        } else if (other.unauthenticatedResumeFile != null) return false
        if (unauthenticatedResumeFileName != other.unauthenticatedResumeFileName) return false

        return true
    }

    override fun hashCode(): Int {
        var result = jobDescription?.hashCode() ?: 0
        result = 31 * result + (jobImage?.contentHashCode() ?: 0)
        result = 31 * result + (jobImageMimeType?.hashCode() ?: 0)
        result = 31 * result + templateId.hashCode()
        result = 31 * result + (unauthenticatedResumeFile?.contentHashCode() ?: 0)
        result = 31 * result + (unauthenticatedResumeFileName?.hashCode() ?: 0)
        return result
    }
}

@Serializable
data class ApplicationLetterResult(
    val plainText: String,
    val structuredData: StructuredLetterData,
    val templateId: String,
    val templateName: String
)

@Serializable
data class ApplicationLetterResponse(
    val success: Boolean,
    val id: String? = null,
    val edgeFunctionData: EdgeFunctionData? = null,
    val error: String? = null,
)

@Serializable
data class EdgeFunctionData(
    val letterId: String,
    val resumeInput: ResumeInputData,
    val jobInput: JobInputData,
    val templateId: String
)

@Serializable
data class ResumeInputData(
    val file: FileData? = null
)

@Serializable
data class JobInputData(
    val description: String? = null,
    val image: FileData? = null
)

@Serializable
data class FileData(
    val buffer: String,
    val mimeType: String
)