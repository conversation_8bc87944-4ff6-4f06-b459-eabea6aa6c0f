package io.gigsta.data.utils

import io.gigsta.data.model.EdgeFunctionData
import io.gigsta.data.network.SupabaseClient
import io.gigsta.domain.repository.AuthRepository
import io.github.jan.supabase.functions.functions
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.coroutines.delay
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Serializable
data class EdgeFunctionResponse(
    val success: Boolean,
    val letterId: String? = null,
    val structuredData: String? = null,
    val message: String? = null,
    val error: String? = null
)

@Serializable
data class LetterStatus(
    val id: String,
    val status: String, // 'processing', 'done', 'error'
    val plain_text: String? = null,
    val design_html: String? = null,
    val structured_data: JsonElement? = null,
    val created_at: String,
    val updated_at: String,
    val template_id: String,
    val user_id: String? = null
)

class LetterEdgeFunction(
    private val authRepository: AuthRepository
) {
    private val supabaseClient = SupabaseClient.client
    private val json = Json { ignoreUnknownKeys = true }

    /**
     * Call the Supabase edge function to generate a letter (fire and forget)
     * This matches the web implementation pattern where we call the edge function
     * and don't wait for the response - the UI will listen for status changes via realtime
     */
    fun callGenerateLetterEdgeFunctionFireAndForget(
        edgeFunctionData: EdgeFunctionData
    ) {
        // Launch in background scope - don't wait for completion
        CoroutineScope(Dispatchers.Default).launch {
            try {
                val accessToken = authRepository.getCurrentAccessToken()
                if (accessToken.isNullOrBlank()) {
                    println("No access token available for edge function call")
                    return@launch
                }

                // Use Supabase functions API instead of direct HTTP call
                // This automatically handles environment variables and authentication
                val response = supabaseClient.functions.invoke(
                    function = "generate-letter",
                    body = edgeFunctionData
                )

                if (response.status.isSuccess()) {
                    println("Edge function called successfully (fire and forget)")
                } else {
                    val errorBody = response.bodyAsText()
                    println("Edge function error: ${response.status.value} - $errorBody")
                }

            } catch (e: Exception) {
                println("Error calling edge function (fire and forget): ${e.message}")
            }
        }
    }

    /**
     * Call the Supabase edge function to generate a letter (legacy - for backward compatibility)
     */
    suspend fun callGenerateLetterEdgeFunction(
        edgeFunctionData: EdgeFunctionData
    ): Result<EdgeFunctionResponse> {
        return try {
            val accessToken = authRepository.getCurrentAccessToken()
            if (accessToken.isNullOrBlank()) {
                return Result.failure(Exception("No access token available"))
            }

            // Use Supabase functions API instead of direct HTTP call
            // This automatically handles environment variables and authentication
            val response = supabaseClient.functions.invoke(
                function = "generate-letter",
                body = edgeFunctionData
            )

            if (response.status.isSuccess()) {
                val responseData = response.body<EdgeFunctionResponse>()
                Result.success(responseData)
            } else {
                val errorBody = response.bodyAsText()
                Result.failure(Exception("Edge function error: ${response.status.value} - $errorBody"))
            }

        } catch (e: Exception) {
            println("Error calling edge function: ${e.message}")
            Result.failure(Exception("Failed to call edge function: ${e.message}"))
        }
    }

    /**
     * Poll the letter status until completion
     */
    suspend fun pollLetterStatus(
        letterId: String,
        maxAttempts: Int = 60, // 5 minutes with 5-second intervals
        intervalMs: Long = 5000L
    ): Result<LetterStatus> {
        return try {
            val accessToken = authRepository.getCurrentAccessToken()
            if (accessToken.isNullOrBlank()) {
                return Result.failure(Exception("No access token available"))
            }

            repeat(maxAttempts) { attempt ->
                println("Polling letter status, attempt ${attempt + 1}/$maxAttempts")

                // Query the letters table for status
                val statusResult = getLetterStatus(letterId, accessToken)

                when {
                    statusResult.isSuccess -> {
                        val status = statusResult.getOrNull()
                        if (status != null) {
                            when (status.status) {
                                "done" -> {
                                    println("Letter generation completed successfully")
                                    return Result.success(status)
                                }
                                "error" -> {
                                    val errorMessage = "Generation failed"
                                    println("Letter generation failed: $errorMessage")
                                    return Result.failure(Exception(errorMessage))
                                }
                                "processing" -> {
                                    println("Letter generation still in progress...")
                                    // Continue polling
                                }
                                else -> {
                                    println("Unknown status: ${status.status}, treating as processing...")
                                    // Continue polling
                                }
                            }
                        }
                    }
                    else -> {
                        println("Failed to get letter status: ${statusResult.exceptionOrNull()?.message}")
                        // Continue polling in case it's a temporary error
                    }
                }

                if (attempt < maxAttempts - 1) {
                    delay(intervalMs)
                }
            }

            Result.failure(Exception("Letter generation timed out after $maxAttempts attempts"))

        } catch (e: Exception) {
            println("Error polling letter status: ${e.message}")
            Result.failure(Exception("Failed to poll letter status: ${e.message}"))
        }
    }

    /**
     * Get the current status of a letter generation
     */
    suspend fun getLetterStatus(letterId: String, accessToken: String): Result<LetterStatus> {
        return try {
            // Use Supabase REST API to query the letters table
            val response = supabaseClient.httpClient.get("${io.gigsta.data.network.NetworkConfig.SUPABASE_URL}/rest/v1/letters") {
                header(HttpHeaders.Authorization, "Bearer $accessToken")
                header("apikey", io.gigsta.data.network.NetworkConfig.SUPABASE_ANON_KEY)
                parameter("select", "id,status,plain_text,design_html,structured_data,created_at,updated_at,template_id,user_id")
                parameter("id", "eq.$letterId")
            }

            if (response.status.isSuccess()) {
                val responseText = response.bodyAsText()
                println("Letter status response: $responseText")

                // Parse as array since Supabase returns an array
                val statusArray = json.decodeFromString<List<LetterStatus>>(responseText)
                if (statusArray.isNotEmpty()) {
                    Result.success(statusArray.first())
                } else {
                    Result.failure(Exception("Letter not found"))
                }
            } else {
                val errorBody = response.bodyAsText()
                Result.failure(Exception("Failed to get letter status: ${response.status.value} - $errorBody"))
            }

        } catch (e: Exception) {
            println("Error getting letter status: ${e.message}")
            Result.failure(Exception("Failed to get letter status: ${e.message}"))
        }
    }
}