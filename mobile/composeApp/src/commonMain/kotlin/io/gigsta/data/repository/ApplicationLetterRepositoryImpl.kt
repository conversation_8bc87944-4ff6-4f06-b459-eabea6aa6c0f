package io.gigsta.data.repository

import io.gigsta.data.datasource.ApplicationLetterApiService
import io.gigsta.data.utils.LetterEdgeFunction
import io.gigsta.domain.model.JobInfo
import io.gigsta.domain.model.ResumeInfo
import io.gigsta.domain.repository.ApplicationLetterRepository
import io.gigsta.domain.repository.AuthRepository
import io.gigsta.presentation.applicationletter.ApplicationLetter

class ApplicationLetterRepositoryImpl(
    private val applicationLetterApiService: ApplicationLetterApiService,
    private val authRepository: AuthRepository
) : ApplicationLetterRepository {

    private val letterEdgeFunction = LetterEdgeFunction(authRepository)
    override suspend fun generateApplicationLetter(
        resumeInfo: ResumeInfo,
        jobInfo: JobInfo,
        templateId: String
    ): Result<ApplicationLetter> {
        return try {
            println("Starting application letter generation...")

            // Step 1: Call the API to get generation ID and edge function data
            val apiResult = applicationLetterApiService.generateApplicationLetter(
                resumeInfo,
                jobInfo,
                templateId,
                authRepository.getCurrentAccessToken()
            )

            apiResult.fold(
                onSuccess = { response ->
                    if (response.success && response.id != null && response.edgeFunctionData != null) {
                        println("API call successful, got letter ID: ${response.id}")

                        // Step 2: Call the edge function (fire and forget)
                        println("Calling edge function (fire and forget)...")
                        letterEdgeFunction.callGenerateLetterEdgeFunctionFireAndForget(
                            response.edgeFunctionData
                        )

                        // Step 3: Return immediately with a processing status
                        // The UI will listen for status changes via realtime subscription
                        Result.success(
                            ApplicationLetter(
                                id = response.id,
                                plainText = "", // Will be populated when generation completes
                                structuredData = io.gigsta.domain.utils.createDefaultStructuredLetterData(
                                    templateId = templateId,
                                    language = "id"
                                ),
                                templateId = templateId,
                                templateName = "Generated Letter"
                            )
                        )
                    } else {
                        val errorMsg = response.error ?: "API response missing required data"
                        println("API call failed: $errorMsg")
                        Result.failure(Exception(errorMsg))
                    }
                },
                onFailure = { exception ->
                    println("API call failed: ${exception.message}")
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            println("Application letter generation error: ${e.message}")
            Result.failure(Exception("Failed to generate application letter: ${e.message}"))
        }
    }
}