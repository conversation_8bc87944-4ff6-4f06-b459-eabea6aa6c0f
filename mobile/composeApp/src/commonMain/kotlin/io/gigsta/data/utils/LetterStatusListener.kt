package io.gigsta.data.utils

import io.gigsta.data.network.SupabaseClient
import io.gigsta.domain.repository.AuthRepository
import io.github.jan.supabase.postgrest.query.filter.FilterOperator
import io.github.jan.supabase.realtime.PostgresAction
import io.github.jan.supabase.realtime.channel
import io.github.jan.supabase.realtime.decodeRecord
import io.github.jan.supabase.realtime.postgresChangeFlow
import io.github.jan.supabase.realtime.realtime
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlin.time.Clock
import kotlin.time.ExperimentalTime

@Serializable
data class LetterStatusUpdate(
    val id: String,
    val status: String, // 'processing', 'done', 'error'
    val plain_text: String? = null,
    val design_html: String? = null,
    val structured_data: JsonElement? = null,
    val created_at: String,
    val updated_at: String,
    val template_id: String,
    val user_id: String? = null
)

sealed class LetterGenerationStatus {
    object Idle : LetterGenerationStatus()
    data class Processing(val startedAt: Long? = null) : LetterGenerationStatus()
    data class Done(
        val plainText: String,
        val designHtml: String?,
        val structuredData: String?,
        val startedAt: Long
    ) : LetterGenerationStatus()
    data class Error(
        val error: String,
        val startedAt: Long? = null
    ) : LetterGenerationStatus()
}

class LetterStatusListener(
    private val authRepository: AuthRepository
) {
    private val supabaseClient = SupabaseClient.client
    private val json = Json { ignoreUnknownKeys = true }

    /**
     * Listen for letter status changes via Supabase realtime
     * This matches the web implementation pattern using realtime subscriptions
     */
    suspend fun listenForLetterStatusChanges(letterId: String): Flow<LetterGenerationStatus> {
        val channel = supabaseClient.realtime.channel("letter_${letterId}_changes")

        val changeFlow = channel.postgresChangeFlow<PostgresAction.Update>(schema = "public") {
            table = "letters"
            filter("id", FilterOperator.EQ, letterId)
        }

        // Subscribe to the channel to start receiving updates
        channel.subscribe()

        return changeFlow.map { change ->
            val letterUpdate = change.decodeRecord<LetterStatusUpdate>()
            transformLetterStatus(letterUpdate)
        }
    }

    /**
     * Get initial letter status from database
     */
    suspend fun getInitialLetterStatus(letterId: String): Result<LetterGenerationStatus> {
        return try {
            val accessToken = authRepository.getCurrentAccessToken()
            if (accessToken.isNullOrBlank()) {
                return Result.failure(Exception("No access token available"))
            }

            // Use the existing LetterEdgeFunction to get status
            val letterEdgeFunction = LetterEdgeFunction(authRepository)
            val statusResult = letterEdgeFunction.getLetterStatus(letterId, accessToken)

            statusResult.fold(
                onSuccess = { letterStatus ->
                    val transformedStatus = transformLetterStatus(
                        LetterStatusUpdate(
                            id = letterStatus.id,
                            status = letterStatus.status,
                            plain_text = letterStatus.plain_text,
                            design_html = letterStatus.design_html,
                            structured_data = letterStatus.structured_data,
                            created_at = letterStatus.created_at,
                            updated_at = letterStatus.updated_at,
                            template_id = letterStatus.template_id,
                            user_id = letterStatus.user_id
                        )
                    )
                    Result.success(transformedStatus)
                },
                onFailure = { exception ->
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Result.failure(Exception("Failed to get initial letter status: ${e.message}"))
        }
    }

    @OptIn(ExperimentalTime::class)
    private fun transformLetterStatus(letterUpdate: LetterStatusUpdate): LetterGenerationStatus {
        return when (letterUpdate.status) {
            "done" -> {
                val plainText = letterUpdate.plain_text ?: ""
                val designHtml = letterUpdate.design_html
                val structuredData = letterUpdate.structured_data?.toString()
                val startedAt = try {
                    // Parse ISO timestamp to milliseconds
                    kotlinx.datetime.Instant.parse(letterUpdate.created_at).toEpochMilliseconds()
                } catch (e: Exception) {
                    Clock.System.now().toEpochMilliseconds()
                }

                LetterGenerationStatus.Done(
                    plainText = plainText,
                    designHtml = designHtml,
                    structuredData = structuredData,
                    startedAt = startedAt
                )
            }
            "error" -> {
                val error = "Generation failed"
                val startedAt = try {
                    kotlinx.datetime.Instant.parse(letterUpdate.created_at).toEpochMilliseconds()
                } catch (e: Exception) {
                    null
                }

                LetterGenerationStatus.Error(
                    error = error,
                    startedAt = startedAt
                )
            }
            "processing" -> {
                val startedAt = try {
                    kotlinx.datetime.Instant.parse(letterUpdate.created_at).toEpochMilliseconds()
                } catch (e: Exception) {
                    null
                }

                LetterGenerationStatus.Processing(startedAt = startedAt)
            }
            else -> {
                LetterGenerationStatus.Processing()
            }
        }
    }
}
