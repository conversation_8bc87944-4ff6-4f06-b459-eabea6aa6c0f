package io.gigsta.utils

import android.content.Context
import android.content.Intent
import android.net.Uri

actual class EmailSender(private val context: Context) {
    actual fun sendEmail(
        subject: String,
        body: String,
        recipientEmail: String?
    ) {
        try {
            val intent = Intent(Intent.ACTION_SENDTO).apply {
                data = Uri.parse("mailto:")
                putExtra(Intent.EXTRA_SUBJECT, subject)
                putExtra(Intent.EXTRA_TEXT, body)

                // Add recipient if provided
                recipientEmail?.let { email ->
                    putExtra(Intent.EXTRA_EMAIL, arrayOf(email))
                }

                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }

            // Create chooser to ensure user sees all email options
            val chooser = Intent.createChooser(intent, "Kirim Email")
            chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

            context.startActivity(chooser)
        } catch (e: Exception) {
            println("Error opening email client: ${e.message}")
        }
    }
}