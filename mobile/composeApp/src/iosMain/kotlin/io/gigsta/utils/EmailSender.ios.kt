package io.gigsta.utils

import platform.Foundation.NSURL
import platform.Foundation.NSURLComponents
import platform.Foundation.NSURLQueryItem
import platform.UIKit.UIApplication

actual class EmailSender {
    actual fun sendEmail(
        subject: String,
        body: String,
        recipientEmail: String?
    ) {
        try {
            val urlComponents = NSURLComponents().apply {
                scheme = "mailto"
                path = recipientEmail ?: ""

                val queryItems = mutableListOf<NSURLQueryItem>()
                queryItems.add(NSURLQueryItem.queryItemWithName("subject", subject))
                queryItems.add(NSURLQueryItem.queryItemWithName("body", body))

                this.queryItems = queryItems
            }

            val mailtoUrl = urlComponents.URL
            if (mailtoUrl != null && UIApplication.sharedApplication.canOpenURL(mailtoUrl)) {
                UIApplication.sharedApplication.openURL(mailtoUrl)
            } else {
                println("No email client available")
            }
        } catch (e: Exception) {
            println("Error opening email client: ${e.message}")
        }
    }
}